using shared.Converters;
using System.Text.Json.Serialization;

namespace shared.Components.ApiEventBus
{
    public class ApiEvent
    {
        [JsonConverter(typeof(JsonHttpMethodConverter))]
        public HttpMethod Method { get; set; } = HttpMethod.Post;
        public string Endpoint { get; set; } = string.Empty;

        [JsonConverter(typeof(JsonTypedConverter))]
        public object? Payload { get; set; }
    }
}
