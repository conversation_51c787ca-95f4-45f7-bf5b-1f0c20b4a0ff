﻿using shared.Converters;
using shared.Models.Enums;
using System.Text.Json.Serialization;

namespace shared.Components.MessageBus.Models
{
    public class MessageBusDispachParams
    {
        [JsonConverter(typeof(JsonEnumStringConverter<MicroserviceType>))]
        public MicroserviceType TargetMicroservice { get; set; }

        /// <summary>
        /// Controller name for routing
        /// </summary>
        public string Controller { get; set; } = string.Empty;

        /// <summary>
        /// Route name for routing
        /// </summary>
        public string Route { get; set; } = string.Empty;

        /// <summary>
        /// HTTP method for the request
        /// </summary>
        [JsonConverter(typeof(JsonHttpMethodConverter))]
        public HttpMethod Method { get; set; } = HttpMethod.Post;

        /// <summary>
        /// The message payload as an object with automatic type conversion
        /// </summary>
        [JsonConverter(typeof(JsonTypedConverter))]
        public object Payload { get; set; } = new object();

        public int DelayInSeconds { get; set; } = 0;

        public MessageBusDispachParams() { }

        public MessageBusDispachParams(MessageBusMessage fromMessage, int delayInSeconds = 0) {
            TargetMicroservice = fromMessage.TargetMicroservice;
            Controller = fromMessage.Controller;
            Route = fromMessage.Route;
            DelayInSeconds = delayInSeconds;
            Method = fromMessage.Method;
            Payload = fromMessage.Payload;
        }
    }
}
